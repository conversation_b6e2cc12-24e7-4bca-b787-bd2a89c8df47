//! ID types for domain entities

use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Macro to create ID types
macro_rules! id_type {
    ($name:ident) => {
        #[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
        pub struct $name(pub Uuid);

        impl From<Uuid> for $name {
            fn from(id: Uuid) -> Self {
                Self(id)
            }
        }

        impl From<$name> for Uuid {
            fn from(id: $name) -> Self {
                id.0
            }
        }

        impl std::fmt::Display for $name {
            fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                write!(f, "{}", self.0)
            }
        }
    };
}

// Subject Journal IDs
id_type!(JournalEntryId);
id_type!(JournalEntryBlockId);
id_type!(JournalEntryAttachmentId);
id_type!(EncounterId);
id_type!(InterventionPeriodId);
id_type!(NoteId);
id_type!(DrugPrescriptionId);
id_type!(ClinicalCodingId);
id_type!(OutboundReferralId);
id_type!(OutboundDoctorsLetterId);
id_type!(OutboundReceiverId);
id_type!(MedicalCertificateId);
id_type!(SubjectHealthProfileId);
id_type!(SubjectStaticDataId);
id_type!(SnippetId);
id_type!(InboundDataId);
id_type!(InboundEntryId);

// Template IDs
id_type!(JournalTemplateId);
id_type!(JournalBlockTemplateId);
id_type!(JournalTemplateSectionId);
id_type!(DrugPrescriptionTemplateId);
id_type!(ClinicalCodingTemplateId);

// Account IDs
id_type!(OrganisationId);
id_type!(ProviderId);
id_type!(SubjectId);
id_type!(AddressId);
id_type!(DepartmentId);

// Team IDs
id_type!(TeamId);

// Calendar IDs
id_type!(EventInstanceId);
id_type!(ParticipantId);
id_type!(ExternalServiceTypeId);
id_type!(AvailabilityScheduleId);
id_type!(EventRecurrenceId);
id_type!(CalendarConfigId);

// Billing IDs
id_type!(BillingInvoiceId);
id_type!(BillingInvoiceLineId);
id_type!(BillingInvoiceIssuerId);
id_type!(BillingCodeNhiId);
id_type!(BillingCodeClinicSpecificId);
id_type!(NhiConfigId);
id_type!(NhiLogId);
id_type!(OnlinePaymentConfigId);

// List IDs
id_type!(AppointmentRequestId);
id_type!(ReferralItemId);
id_type!(DoctorLetterItemId);
id_type!(ListItemId);
id_type!(WaitingListId);

// Entity Event IDs
id_type!(EntityEventId);

// Notification IDs
id_type!(SubjectNotificationsConfigId);
