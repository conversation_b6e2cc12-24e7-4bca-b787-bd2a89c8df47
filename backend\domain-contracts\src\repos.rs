//! Repository interfaces

/// Interface for accessing all repositories
pub trait IRepos<'a>: Send + Sync {
    // Accounts
    fn organisation_repo(&self) -> Box<dyn IOrganisationRepo + 'a>;
    fn provider_repo(&self) -> Box<dyn IProviderRepo + 'a>;
    fn subject_repo(&self) -> Box<dyn ISubjectRepo + 'a>;
    fn address_repo(&self) -> Box<dyn IAddressRepo + 'a>;
    fn department_repo(&self) -> Box<dyn IDepartmentRepo + 'a>;

    // Teams
    fn team_repo(&self) -> Box<dyn ITeamRepo + 'a>;

    // Billing
    fn invoice_repo(&self) -> Box<dyn IBillingInvoiceRepo + 'a>;
    fn invoice_line_repo(&self) -> Box<dyn IBillingInvoiceLineRepo + 'a>;
    fn invoice_issuer_repo(&self) -> Box<dyn IBillingInvoiceIssuerRepo + 'a>;
    fn billing_code_nhi_repo(&self) -> Box<dyn IBillingCodeNhiRepo + 'a>;
    fn billing_code_clinic_specific_repo(&self) -> Box<dyn IBillingCodeClinicSpecificRepo + 'a>;
    fn nhi_config_repo(&self) -> Box<dyn INhiConfigRepo + 'a>;
    fn nhi_billing_log_repo(&self) -> Box<dyn INhiLogRepo + 'a>;
    fn online_payment_config_repo(&self) -> Box<dyn IOnlinePaymentConfigRepo + 'a>;

    // Subject Journal
    fn journal_entry_repo(&self) -> Box<dyn IJournalEntryRepo + 'a>;
    fn drug_prescription_repo(&self) -> Box<dyn IDrugPrescriptionRepo + 'a>;
    fn intervention_period_repo(&self) -> Box<dyn IInterventionPeriodRepo + 'a>;
    fn encounter_repo(&self) -> Box<dyn IEncounterRepo + 'a>;
    fn journal_entry_block_repo(&self) -> Box<dyn IJournalEntryBlockRepo + 'a>;
    fn clinical_coding_repo(&self) -> Box<dyn IClinicalCodingRepo + 'a>;
    fn note_repo(&self) -> Box<dyn INoteRepo + 'a>;
    fn outbound_referral_repo(&self) -> Box<dyn IOutboundReferralRepo + 'a>;
    fn outbound_doctors_letter_repo(&self) -> Box<dyn IOutboundDoctorsLetterRepo + 'a>;
    fn medical_certificate_repo(&self) -> Box<dyn IMedicalCertificateRepo + 'a>;
    fn subject_health_profile_repo(&self) -> Box<dyn ISubjectHealthProfileRepo + 'a>;
    fn journal_entry_attachment_repo(&self) -> Box<dyn IJournalEntryAttachmentRepo + 'a>;
    fn subject_static_data_repo(&self) -> Box<dyn ISubjectStaticDataRepo + 'a>;
    fn snippet_repo(&self) -> Box<dyn ISnippetRepo + 'a>;
    fn journal_focus_log_repo(&self) -> Box<dyn IJournalFocusLogRepo + 'a>;
    fn inbound_data_repo(&self) -> Box<dyn IInboundDataRepo + 'a>;
    fn inbound_entry_repo(&self) -> Box<dyn IInboundEntryRepo + 'a>;

    // Subject Journal Templates
    fn journal_template_repo(&self) -> Box<dyn IJournalTemplateRepo + 'a>;
    fn journal_block_template_repo(&self) -> Box<dyn IJournalBlockTemplateRepo + 'a>;
    fn journal_template_section_repo(&self) -> Box<dyn IJournalTemplateSectionRepo + 'a>;
    fn drug_prescription_template_repo(&self) -> Box<dyn IDrugPrescriptionTemplateRepo + 'a>;
    fn clinical_coding_template_repo(&self) -> Box<dyn IClinicalCodingTemplateRepo + 'a>;

    // Calendar
    fn calendar_config_repo(&self) -> Box<dyn ICalendarConfigRepo + 'a>;
    fn event_instance_repo(&self) -> Box<dyn IEventInstanceRepo + 'a>;
    fn participant_repo(&self) -> Box<dyn IParticipantRepo + 'a>;
    fn service_type_repo(&self) -> Box<dyn IExternalServiceTypeRepo + 'a>;
    fn availability_schedule_repo(&self) -> Box<dyn IAvailabilityScheduleRepo + 'a>;
    fn event_recurrence_repo(&self) -> Box<dyn IEventRecurrenceRepo + 'a>;

    // Entity Events
    fn entity_event_repo(&self) -> Box<dyn IEntityEventRepo + 'a>;

    // Notifications
    fn subject_notifications_config_repo(&self) -> Box<dyn ISubjectNotificationsConfigRepo + 'a>;

    // Lists
    fn appointment_request_repo(&self) -> Box<dyn IAppointmentRequestRepo + 'a>;
    fn referral_item_repo(&self) -> Box<dyn IReferralItemRepo + 'a>;
    fn doctor_letter_item_repo(&self) -> Box<dyn IDoctorLetterItemRepo + 'a>;
    fn list_item_repo(&self) -> Box<dyn IListItemRepo + 'a>;
    fn waiting_list_repo(&self) -> Box<dyn IWaitingListRepo + 'a>;
}

// Account repository interfaces
pub trait IOrganisationRepo: Send + Sync {}
pub trait IProviderRepo: Send + Sync {}
pub trait ISubjectRepo: Send + Sync {}
pub trait IAddressRepo: Send + Sync {}
pub trait IDepartmentRepo: Send + Sync {}

// Team repository interfaces
pub trait ITeamRepo: Send + Sync {}

// Billing repository interfaces
pub trait IBillingInvoiceRepo: Send + Sync {}
pub trait IBillingInvoiceLineRepo: Send + Sync {}
pub trait IBillingInvoiceIssuerRepo: Send + Sync {}
pub trait IBillingCodeNhiRepo: Send + Sync {}
pub trait IBillingCodeClinicSpecificRepo: Send + Sync {}
pub trait INhiConfigRepo: Send + Sync {}
pub trait INhiLogRepo: Send + Sync {}
pub trait IOnlinePaymentConfigRepo: Send + Sync {}

// Subject Journal repository interfaces
pub trait IJournalEntryRepo: Send + Sync {}
pub trait IDrugPrescriptionRepo: Send + Sync {}
pub trait IInterventionPeriodRepo: Send + Sync {}
pub trait IEncounterRepo: Send + Sync {}
pub trait IJournalEntryBlockRepo: Send + Sync {}
pub trait IClinicalCodingRepo: Send + Sync {}
pub trait INoteRepo: Send + Sync {}
pub trait IOutboundReferralRepo: Send + Sync {}
pub trait IOutboundDoctorsLetterRepo: Send + Sync {}
pub trait IMedicalCertificateRepo: Send + Sync {}
pub trait ISubjectHealthProfileRepo: Send + Sync {}
pub trait IJournalEntryAttachmentRepo: Send + Sync {}
pub trait ISubjectStaticDataRepo: Send + Sync {}
pub trait ISnippetRepo: Send + Sync {}
pub trait IJournalFocusLogRepo: Send + Sync {}
pub trait IInboundDataRepo: Send + Sync {}
pub trait IInboundEntryRepo: Send + Sync {}

// Subject Journal Template repository interfaces
pub trait IJournalTemplateRepo: Send + Sync {}
pub trait IJournalBlockTemplateRepo: Send + Sync {}
pub trait IJournalTemplateSectionRepo: Send + Sync {}
pub trait IDrugPrescriptionTemplateRepo: Send + Sync {}
pub trait IClinicalCodingTemplateRepo: Send + Sync {}

// Calendar repository interfaces
pub trait ICalendarConfigRepo: Send + Sync {}
pub trait IEventInstanceRepo: Send + Sync {}
pub trait IParticipantRepo: Send + Sync {}
pub trait IExternalServiceTypeRepo: Send + Sync {}
pub trait IAvailabilityScheduleRepo: Send + Sync {}
pub trait IEventRecurrenceRepo: Send + Sync {}

// Entity Event repository interfaces
pub trait IEntityEventRepo: Send + Sync {}

// Notification repository interfaces
pub trait ISubjectNotificationsConfigRepo: Send + Sync {}

// List repository interfaces
pub trait IAppointmentRequestRepo: Send + Sync {}
pub trait IReferralItemRepo: Send + Sync {}
pub trait IDoctorLetterItemRepo: Send + Sync {}
pub trait IListItemRepo: Send + Sync {}
pub trait IWaitingListRepo: Send + Sync {}
