//! Repository interfaces

use crate::errors::Result;
use async_trait::async_trait;
use leviosa_domain_types::*;

/// Interface for accessing all repositories
pub trait IRepos<'a>: Send + Sync {
    // Accounts
    fn organisation_repo(&self) -> Box<dyn IOrganisationRepo + 'a>;
    fn provider_repo(&self) -> Box<dyn IProviderRepo + 'a>;
    fn subject_repo(&self) -> Box<dyn ISubjectRepo + 'a>;
    fn address_repo(&self) -> Box<dyn IAddressRepo + 'a>;
    fn department_repo(&self) -> Box<dyn IDepartmentRepo + 'a>;

    // Teams
    fn team_repo(&self) -> Box<dyn ITeamRepo + 'a>;

    // Billing
    fn invoice_repo(&self) -> Box<dyn IBillingInvoiceRepo + 'a>;
    fn invoice_line_repo(&self) -> Box<dyn IBillingInvoiceLineRepo + 'a>;
    fn invoice_issuer_repo(&self) -> Box<dyn IBillingInvoiceIssuerRepo + 'a>;
    fn billing_code_nhi_repo(&self) -> Box<dyn IBillingCodeNhiRepo + 'a>;
    fn billing_code_clinic_specific_repo(&self) -> Box<dyn IBillingCodeClinicSpecificRepo + 'a>;
    fn nhi_config_repo(&self) -> Box<dyn INhiConfigRepo + 'a>;
    fn nhi_billing_log_repo(&self) -> Box<dyn INhiLogRepo + 'a>;
    fn online_payment_config_repo(&self) -> Box<dyn IOnlinePaymentConfigRepo + 'a>;

    // Subject Journal
    fn journal_entry_repo(&self) -> Box<dyn IJournalEntryRepo + 'a>;
    fn drug_prescription_repo(&self) -> Box<dyn IDrugPrescriptionRepo + 'a>;
    fn intervention_period_repo(&self) -> Box<dyn IInterventionPeriodRepo + 'a>;
    fn encounter_repo(&self) -> Box<dyn IEncounterRepo + 'a>;
    fn journal_entry_block_repo(&self) -> Box<dyn IJournalEntryBlockRepo + 'a>;
    fn clinical_coding_repo(&self) -> Box<dyn IClinicalCodingRepo + 'a>;
    fn note_repo(&self) -> Box<dyn INoteRepo + 'a>;
    fn outbound_referral_repo(&self) -> Box<dyn IOutboundReferralRepo + 'a>;
    fn outbound_doctors_letter_repo(&self) -> Box<dyn IOutboundDoctorsLetterRepo + 'a>;
    fn medical_certificate_repo(&self) -> Box<dyn IMedicalCertificateRepo + 'a>;
    fn subject_health_profile_repo(&self) -> Box<dyn ISubjectHealthProfileRepo + 'a>;
    fn journal_entry_attachment_repo(&self) -> Box<dyn IJournalEntryAttachmentRepo + 'a>;
    fn subject_static_data_repo(&self) -> Box<dyn ISubjectStaticDataRepo + 'a>;
    fn snippet_repo(&self) -> Box<dyn ISnippetRepo + 'a>;
    fn journal_focus_log_repo(&self) -> Box<dyn IJournalFocusLogRepo + 'a>;
    fn inbound_data_repo(&self) -> Box<dyn IInboundDataRepo + 'a>;
    fn inbound_entry_repo(&self) -> Box<dyn IInboundEntryRepo + 'a>;

    // Subject Journal Templates
    fn journal_template_repo(&self) -> Box<dyn IJournalTemplateRepo + 'a>;
    fn journal_block_template_repo(&self) -> Box<dyn IJournalBlockTemplateRepo + 'a>;
    fn journal_template_section_repo(&self) -> Box<dyn IJournalTemplateSectionRepo + 'a>;
    fn drug_prescription_template_repo(&self) -> Box<dyn IDrugPrescriptionTemplateRepo + 'a>;
    fn clinical_coding_template_repo(&self) -> Box<dyn IClinicalCodingTemplateRepo + 'a>;

    // Calendar
    fn calendar_config_repo(&self) -> Box<dyn ICalendarConfigRepo + 'a>;
    fn event_instance_repo(&self) -> Box<dyn IEventInstanceRepo + 'a>;
    fn participant_repo(&self) -> Box<dyn IParticipantRepo + 'a>;
    fn service_type_repo(&self) -> Box<dyn IExternalServiceTypeRepo + 'a>;
    fn availability_schedule_repo(&self) -> Box<dyn IAvailabilityScheduleRepo + 'a>;
    fn event_recurrence_repo(&self) -> Box<dyn IEventRecurrenceRepo + 'a>;

    // Entity Events
    fn entity_event_repo(&self) -> Box<dyn IEntityEventRepo + 'a>;

    // Notifications
    fn subject_notifications_config_repo(&self) -> Box<dyn ISubjectNotificationsConfigRepo + 'a>;

    // Lists
    fn appointment_request_repo(&self) -> Box<dyn IAppointmentRequestRepo + 'a>;
    fn referral_item_repo(&self) -> Box<dyn IReferralItemRepo + 'a>;
    fn doctor_letter_item_repo(&self) -> Box<dyn IDoctorLetterItemRepo + 'a>;
    fn list_item_repo(&self) -> Box<dyn IListItemRepo + 'a>;
    fn waiting_list_repo(&self) -> Box<dyn IWaitingListRepo + 'a>;
}

// Account repository interfaces
pub trait IOrganisationRepo: Send + Sync {
    // TODO: Add organisation repository methods
}

pub trait IProviderRepo: Send + Sync {
    // TODO: Add provider repository methods
}

pub trait ISubjectRepo: Send + Sync {
    // TODO: Add subject repository methods
}

pub trait IAddressRepo: Send + Sync {
    // TODO: Add address repository methods
}

pub trait IDepartmentRepo: Send + Sync {
    // TODO: Add department repository methods
}

// Team repository interfaces
pub trait ITeamRepo: Send + Sync {
    // TODO: Add team repository methods
}

// Billing repository interfaces
pub trait IBillingInvoiceRepo: Send + Sync {
    // TODO: Add billing invoice repository methods
}

pub trait IBillingInvoiceLineRepo: Send + Sync {
    // TODO: Add billing invoice line repository methods
}

pub trait IBillingInvoiceIssuerRepo: Send + Sync {
    // TODO: Add billing invoice issuer repository methods
}

pub trait IBillingCodeNhiRepo: Send + Sync {
    // TODO: Add billing code NHI repository methods
}

pub trait IBillingCodeClinicSpecificRepo: Send + Sync {
    // TODO: Add billing code clinic specific repository methods
}

pub trait INhiConfigRepo: Send + Sync {
    // TODO: Add NHI config repository methods
}

pub trait INhiLogRepo: Send + Sync {
    // TODO: Add NHI log repository methods
}

pub trait IOnlinePaymentConfigRepo: Send + Sync {
    // TODO: Add online payment config repository methods
}

// Subject Journal repository interfaces
#[async_trait]
pub trait IJournalEntryRepo: Send + Sync {
    // Note: These are placeholder signatures using generic types
    // The actual domain entities will be provided by the implementor
    async fn get(&self, filter: GetJournalEntryFilter) -> Result<serde_json::Value>;
    async fn find(&self, filter: FindJournalEntryFilter) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<JournalEntryEvent>,
    ) -> Result<serde_json::Value>;
}

#[async_trait]
pub trait IDrugPrescriptionRepo: Send + Sync {
    async fn get(&self, id: DrugPrescriptionId) -> Result<serde_json::Value>;
    async fn get_by_journal_entry_ids(
        &self,
        journal_entry_ids: &[JournalEntryId],
    ) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<DrugPrescriptionEvent>,
    ) -> Result<serde_json::Value>;
}

#[async_trait]
pub trait IInterventionPeriodRepo: Send + Sync {
    async fn get(&self, id: InterventionPeriodId) -> Result<serde_json::Value>;
    async fn get_by_external_ehr_id(
        &self,
        external_ehr_id: String,
    ) -> Result<Option<serde_json::Value>>;
    async fn get_many(&self, ids: &[InterventionPeriodId]) -> Result<Vec<serde_json::Value>>;
    async fn find(&self, filter: InterventionPeriodFilter) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<InterventionPeriodEvent>,
    ) -> Result<serde_json::Value>;
}

#[async_trait]
pub trait IEncounterRepo: Send + Sync {
    async fn get(&self, id: EncounterId) -> Result<serde_json::Value>;
    async fn get_subject_location_by_encounter_id(
        &self,
        id: EncounterId,
    ) -> Result<Option<serde_json::Value>>;
    async fn find(&self, filter: FindEncountersFilter) -> Result<Vec<serde_json::Value>>;
    async fn add_responsible_providers(
        &self,
        model: Vec<serde_json::Value>,
        events: Vec<EncounterEvent>,
    ) -> Result<()>;
    async fn remove_responsible_providers(
        &self,
        models: Vec<serde_json::Value>,
        events: Vec<EncounterEvent>,
    ) -> Result<()>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<EncounterEvent>,
    ) -> Result<serde_json::Value>;
    async fn save_subject_location(
        &self,
        model: serde_json::Value,
        events: Vec<EncounterEvent>,
    ) -> Result<serde_json::Value>;
}

#[async_trait]
pub trait IJournalEntryBlockRepo: Send + Sync {
    async fn get(&self, id: JournalEntryBlockId) -> Result<serde_json::Value>;
    async fn get_by_journal_entry_ids(
        &self,
        journal_entry_ids: &[JournalEntryId],
    ) -> Result<Option<Vec<serde_json::Value>>>;
    async fn find(&self, filter: serde_json::Value) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<serde_json::Value>,
    ) -> Result<serde_json::Value>;
}

#[async_trait]
pub trait IClinicalCodingRepo: Send + Sync {
    async fn get(&self, id: ClinicalCodingId) -> Result<serde_json::Value>;
    async fn get_by_journal_entry_ids(
        &self,
        journal_entry_ids: &[JournalEntryId],
    ) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<ClinicalCodingEvent>,
    ) -> Result<serde_json::Value>;
}

#[async_trait]
pub trait INoteRepo: Send + Sync {
    async fn get(&self, id: NoteId) -> Result<serde_json::Value>;
    async fn find(&self, filter: FindNotesFilter) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<NoteEvent>,
    ) -> Result<serde_json::Value>;
    async fn save_many(
        &self,
        models: Vec<serde_json::Value>,
        events: Vec<NoteEvent>,
    ) -> Result<Vec<serde_json::Value>>;
}

#[async_trait]
pub trait IOutboundReferralRepo: Send + Sync {
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<OutboundReferralEvent>,
    ) -> Result<serde_json::Value>;
    async fn get(&self, id: OutboundReferralId) -> Result<serde_json::Value>;
    async fn get_receivers(&self, ids: Vec<OutboundReceiverId>) -> Result<Vec<serde_json::Value>>;
    async fn save_receivers(
        &self,
        models: Vec<serde_json::Value>,
    ) -> Result<Vec<serde_json::Value>>;
    async fn get_receiver(&self, id: OutboundReceiverId) -> Result<serde_json::Value>;
    async fn save_receiver(&self, model: serde_json::Value) -> Result<serde_json::Value>;
    async fn get_by_journal_entry_ids(
        &self,
        ids: &[JournalEntryId],
    ) -> Result<Vec<serde_json::Value>>;
}

#[async_trait]
pub trait IOutboundDoctorsLetterRepo: Send + Sync {
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<OutboundDoctorsLetterEvent>,
    ) -> Result<serde_json::Value>;
    async fn get(&self, id: OutboundDoctorsLetterId) -> Result<serde_json::Value>;
    async fn get_receivers(&self, ids: Vec<OutboundReceiverId>) -> Result<Vec<serde_json::Value>>;
    async fn save_receivers(
        &self,
        models: Vec<serde_json::Value>,
    ) -> Result<Vec<serde_json::Value>>;
    async fn get_receiver(&self, id: OutboundReceiverId) -> Result<serde_json::Value>;
    async fn save_receiver(&self, model: serde_json::Value) -> Result<serde_json::Value>;
    async fn get_by_journal_entry_ids(
        &self,
        ids: &[JournalEntryId],
    ) -> Result<Vec<serde_json::Value>>;
}

pub trait IMedicalCertificateRepo: Send + Sync {
    // TODO: Add medical certificate repository methods
}

pub trait ISubjectHealthProfileRepo: Send + Sync {
    // TODO: Add subject health profile repository methods
}

#[async_trait]
pub trait IJournalEntryAttachmentRepo: Send + Sync {
    async fn get(&self, filter: GetJournalEntryAttachmentFilter) -> Result<serde_json::Value>;
    async fn find(
        &self,
        filter: FindJournalEntryAttachmentFilter,
    ) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<JournalEntryAttachmentEvent>,
    ) -> Result<serde_json::Value>;
}

pub trait ISubjectStaticDataRepo: Send + Sync {
    // TODO: Add subject static data repository methods
}

pub trait ISnippetRepo: Send + Sync {
    // TODO: Add snippet repository methods
}

pub trait IJournalFocusLogRepo: Send + Sync {
    // TODO: Add journal focus log repository methods
}

#[async_trait]
pub trait IInboundDataRepo: Send + Sync {
    async fn get(&self, filter: GetInboundDataFilter) -> Result<serde_json::Value>;
    async fn find(&self, filter: FindInboundDataFilter) -> Result<Vec<serde_json::Value>>;
    async fn save(
        &self,
        model: serde_json::Value,
        events: Vec<InboundDataEvent>,
    ) -> Result<serde_json::Value>;
    async fn create_receiver_info(
        &self,
        model: serde_json::Value,
        events: Vec<InboundDataEvent>,
    ) -> Result<serde_json::Value>;
    async fn find_receiver_info(
        &self,
        ids: Vec<serde_json::Value>,
    ) -> Result<Vec<serde_json::Value>>;
    async fn create_external_sender_info(
        &self,
        model: serde_json::Value,
        events: Vec<InboundDataEvent>,
    ) -> Result<serde_json::Value>;
}

#[async_trait]
pub trait IInboundEntryRepo: Send + Sync {
    async fn get(&self, id: InboundEntryId) -> Result<serde_json::Value>;
    async fn find(&self, filter: FindInboundEntryFilter) -> Result<Vec<serde_json::Value>>;
    async fn create(
        &self,
        model: serde_json::Value,
        events: Vec<InboundEntryEvent>,
    ) -> Result<serde_json::Value>;
}

// Subject Journal Template repository interfaces
pub trait IJournalTemplateRepo: Send + Sync {}
pub trait IJournalBlockTemplateRepo: Send + Sync {}
pub trait IJournalTemplateSectionRepo: Send + Sync {}
pub trait IDrugPrescriptionTemplateRepo: Send + Sync {}
pub trait IClinicalCodingTemplateRepo: Send + Sync {}

// Calendar repository interfaces
pub trait ICalendarConfigRepo: Send + Sync {}
pub trait IEventInstanceRepo: Send + Sync {}
pub trait IParticipantRepo: Send + Sync {}
pub trait IExternalServiceTypeRepo: Send + Sync {}
pub trait IAvailabilityScheduleRepo: Send + Sync {}
pub trait IEventRecurrenceRepo: Send + Sync {}

// Entity Event repository interfaces
pub trait IEntityEventRepo: Send + Sync {}

// Notification repository interfaces
pub trait ISubjectNotificationsConfigRepo: Send + Sync {}

// List repository interfaces
pub trait IAppointmentRequestRepo: Send + Sync {}
pub trait IReferralItemRepo: Send + Sync {}
pub trait IDoctorLetterItemRepo: Send + Sync {}
pub trait IListItemRepo: Send + Sync {}
pub trait IWaitingListRepo: Send + Sync {}
