use crate::accounts::{AddressRepo, DepartmentRepo, OrganisationRepo, ProviderRepo, SubjectRepo};
use crate::auth::AuthenticatedUser;
use crate::billing::{
    BillingCodeClinicSpecificRepo, BillingCodeNhiRepo, BillingInvoiceIssuerRepo,
    BillingInvoiceLineRepo, BillingInvoiceRepo, NhiConfigRepo, NhiLogRepo, OnlinePaymentConfigRepo,
};
use crate::calendar::EventInstanceRepo;
use crate::subject_journal::{
    ClinicalCodingRepo, DrugPrescriptionRepo, EncounterRepo, InterventionPeriodRepo,
    JournalEntryAttachmentRepo, JournalEntryBlockRepo, JournalEntryRepo, JournalFocusLogRepo,
    MedicalCertificateRepo, NoteRepo, OutboundDoctorsLetterRepo, OutboundReferralRepo, SnippetRepo,
    SubjectHealthProfileRepo, SubjectStaticDataRepo,
};

// Re-export interfaces from domain-contracts
pub use leviosa_domain_contracts::{
    repo_connection::IRepoConnection,
    repos::{
        IAddressRepo, IAppointmentRequestRepo, IAvailabilityScheduleRepo,
        IBillingCodeClinicSpecificRepo, IBillingCodeNhiRepo, IBillingInvoiceIssuerRepo,
        IBillingInvoiceLineRepo, IBillingInvoiceRepo, ICalendarConfigRepo, IClinicalCodingRepo,
        IClinicalCodingTemplateRepo, IDepartmentRepo, IDoctorLetterItemRepo, IDrugPrescriptionRepo,
        IDrugPrescriptionTemplateRepo, IEncounterRepo, IEntityEventRepo, IEventInstanceRepo,
        IEventRecurrenceRepo, IExternalServiceTypeRepo, IInboundDataRepo, IInboundEntryRepo,
        IInterventionPeriodRepo, IJournalBlockTemplateRepo, IJournalEntryAttachmentRepo,
        IJournalEntryBlockRepo, IJournalEntryRepo, IJournalFocusLogRepo, IJournalTemplateRepo,
        IJournalTemplateSectionRepo, IListItemRepo, IMedicalCertificateRepo, INhiConfigRepo,
        INhiLogRepo, INoteRepo, IOnlinePaymentConfigRepo, IOrganisationRepo,
        IOutboundDoctorsLetterRepo, IOutboundReferralRepo, IParticipantRepo, IProviderRepo,
        IReferralItemRepo, IRepos, ISnippetRepo, ISubjectHealthProfileRepo,
        ISubjectNotificationsConfigRepo, ISubjectRepo, ISubjectStaticDataRepo, ITeamRepo,
        IWaitingListRepo,
    },
};

pub struct Repos<'a, C: IRepoConnection> {
    pub(super) connection: &'a C,
    pub(super) user: &'a AuthenticatedUser,
}

impl<'a, C: IRepoConnection> IRepos<'a> for Repos<'a, C> {
    // Accounts

    fn organisation_repo(&self) -> Box<dyn IOrganisationRepo + 'a> {
        Box::new(OrganisationRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn provider_repo(&self) -> Box<dyn IProviderRepo + 'a> {
        Box::new(ProviderRepo::new(self.connection.connection(), self.user))
    }

    fn subject_repo(&self) -> Box<dyn ISubjectRepo + 'a> {
        Box::new(SubjectRepo::new(self.connection.connection(), self.user))
    }

    fn address_repo(&self) -> Box<dyn IAddressRepo + 'a> {
        Box::new(AddressRepo::new(self.connection.connection(), self.user))
    }

    fn department_repo(&self) -> Box<dyn IDepartmentRepo + 'a> {
        Box::new(DepartmentRepo::new(self.connection.connection(), self.user))
    }

    // Teams

    fn team_repo(&self) -> Box<dyn ITeamRepo + 'a> {
        Box::new(crate::team::TeamRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    // Billing

    fn invoice_repo(&self) -> Box<dyn IBillingInvoiceRepo + 'a> {
        Box::new(BillingInvoiceRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn invoice_line_repo(&self) -> Box<dyn IBillingInvoiceLineRepo + 'a> {
        Box::new(BillingInvoiceLineRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn invoice_issuer_repo(&self) -> Box<dyn IBillingInvoiceIssuerRepo + 'a> {
        Box::new(BillingInvoiceIssuerRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn billing_code_nhi_repo(&self) -> Box<dyn IBillingCodeNhiRepo + 'a> {
        Box::new(BillingCodeNhiRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn billing_code_clinic_specific_repo(&self) -> Box<dyn IBillingCodeClinicSpecificRepo + 'a> {
        Box::new(BillingCodeClinicSpecificRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn nhi_config_repo(&self) -> Box<dyn INhiConfigRepo + 'a> {
        Box::new(NhiConfigRepo::new(self.connection.connection(), self.user))
    }

    fn online_payment_config_repo(&self) -> Box<dyn IOnlinePaymentConfigRepo + 'a> {
        Box::new(OnlinePaymentConfigRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn nhi_billing_log_repo(&self) -> Box<dyn INhiLogRepo + 'a> {
        Box::new(NhiLogRepo::new(self.connection.connection(), self.user))
    }
    // Subject Journal

    fn journal_entry_repo(&self) -> Box<dyn IJournalEntryRepo + 'a> {
        Box::new(JournalEntryRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn drug_prescription_repo(&self) -> Box<dyn IDrugPrescriptionRepo + 'a> {
        Box::new(DrugPrescriptionRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn intervention_period_repo(&self) -> Box<dyn IInterventionPeriodRepo + 'a> {
        Box::new(InterventionPeriodRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn encounter_repo(&self) -> Box<dyn IEncounterRepo + 'a> {
        Box::new(EncounterRepo::new(self.connection.connection(), self.user))
    }

    fn journal_entry_block_repo(&self) -> Box<dyn IJournalEntryBlockRepo + 'a> {
        Box::new(JournalEntryBlockRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn clinical_coding_repo(&self) -> Box<dyn IClinicalCodingRepo + 'a> {
        Box::new(ClinicalCodingRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn note_repo(&self) -> Box<dyn INoteRepo + 'a> {
        Box::new(NoteRepo::new(self.connection.connection(), self.user))
    }

    fn outbound_referral_repo(&self) -> Box<dyn IOutboundReferralRepo + 'a> {
        Box::new(OutboundReferralRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn outbound_doctors_letter_repo(&self) -> Box<dyn IOutboundDoctorsLetterRepo + 'a> {
        Box::new(OutboundDoctorsLetterRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn medical_certificate_repo(&self) -> Box<dyn IMedicalCertificateRepo + 'a> {
        Box::new(MedicalCertificateRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn subject_health_profile_repo(&self) -> Box<dyn ISubjectHealthProfileRepo + 'a> {
        Box::new(SubjectHealthProfileRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn journal_entry_attachment_repo(&self) -> Box<dyn IJournalEntryAttachmentRepo + 'a> {
        Box::new(JournalEntryAttachmentRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn subject_static_data_repo(&self) -> Box<dyn ISubjectStaticDataRepo + 'a> {
        Box::new(SubjectStaticDataRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn snippet_repo(&self) -> Box<dyn ISnippetRepo + 'a> {
        Box::new(SnippetRepo::new(self.connection.connection(), self.user))
    }

    fn journal_focus_log_repo(&self) -> Box<dyn IJournalFocusLogRepo + 'a> {
        Box::new(JournalFocusLogRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn inbound_data_repo(&self) -> Box<dyn IInboundDataRepo + 'a> {
        Box::new(crate::subject_journal::InboundDataRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn inbound_entry_repo(&self) -> Box<dyn IInboundEntryRepo + 'a> {
        Box::new(crate::subject_journal::InboundEntryRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    // Subject Journal Templates

    fn journal_template_repo(&self) -> Box<dyn IJournalTemplateRepo + 'a> {
        Box::new(crate::subject_journal::JournalTemplateRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn journal_block_template_repo(&self) -> Box<dyn IJournalBlockTemplateRepo + 'a> {
        Box::new(crate::subject_journal::JournalBlockTemplateRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn journal_template_section_repo(&self) -> Box<dyn IJournalTemplateSectionRepo + 'a> {
        Box::new(crate::subject_journal::JournalTemplateSectionRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn drug_prescription_template_repo(&self) -> Box<dyn IDrugPrescriptionTemplateRepo + 'a> {
        Box::new(crate::subject_journal::DrugPrescriptionTemplateRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn clinical_coding_template_repo(&self) -> Box<dyn IClinicalCodingTemplateRepo + 'a> {
        Box::new(crate::subject_journal::ClinicalCodingTemplateRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    // Calendar

    fn calendar_config_repo(&self) -> Box<dyn ICalendarConfigRepo + 'a> {
        Box::new(crate::calendar::CalendarConfigRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn event_instance_repo(&self) -> Box<dyn IEventInstanceRepo + 'a> {
        Box::new(EventInstanceRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn participant_repo(&self) -> Box<dyn IParticipantRepo + 'a> {
        Box::new(crate::calendar::ParticipantRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn service_type_repo(&self) -> Box<dyn IExternalServiceTypeRepo + 'a> {
        Box::new(crate::calendar::ExternalServiceTypeRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn availability_schedule_repo(&self) -> Box<dyn IAvailabilityScheduleRepo + 'a> {
        Box::new(crate::calendar::AvailabilityScheduleRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn event_recurrence_repo(&self) -> Box<dyn IEventRecurrenceRepo + 'a> {
        Box::new(crate::calendar::EventRecurrenceRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    // Entity Events

    fn entity_event_repo(&self) -> Box<dyn IEntityEventRepo + 'a> {
        Box::new(crate::entity_event::EntityEventRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    // Notifications

    fn subject_notifications_config_repo(&self) -> Box<dyn ISubjectNotificationsConfigRepo + 'a> {
        Box::new(crate::accounts::SubjectNotificationsConfigRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    // Lists

    fn appointment_request_repo(&self) -> Box<dyn IAppointmentRequestRepo + 'a> {
        Box::new(crate::lists::AppointmentRequestRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn referral_item_repo(&self) -> Box<dyn IReferralItemRepo + 'a> {
        Box::new(crate::lists::ReferralItemRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn doctor_letter_item_repo(&self) -> Box<dyn IDoctorLetterItemRepo + 'a> {
        Box::new(crate::lists::DoctorLetterItemRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn list_item_repo(&self) -> Box<dyn IListItemRepo + 'a> {
        Box::new(crate::lists::ListItemRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }

    fn waiting_list_repo(&self) -> Box<dyn IWaitingListRepo + 'a> {
        Box::new(crate::lists::waiting_list_repo::WaitingListRepo::new(
            self.connection.connection(),
            self.user,
        ))
    }
}
